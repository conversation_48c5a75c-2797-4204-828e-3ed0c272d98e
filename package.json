{"name": "ticketing-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:fast": "next dev --turbopack --experimental-https", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:debug": "next build --debug", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.23.3", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "dompurify": "^3.2.6", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "slate": "^0.117.2", "slate-react": "^0.117.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.75", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.30.1", "@next/bundle-analyzer": "^15.3.5", "@next/eslint-plugin-next": "^15.3.5", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0"}}