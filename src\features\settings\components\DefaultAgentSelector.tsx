'use client';

import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  memo,
  startTransition,
} from 'react';

import { Label } from '@/features/shared/components/ui/label';
import { Switch } from '@/features/shared/components/ui/switch';
import { UserAutocomplete } from '@/features/shared/components/UserAutocomplete';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { useAdminSettings } from '../hooks/useSettingsSync';
import { useUserDetails } from '@/features/shared/hooks/useUserSearch';
import { User, X, AlertCircle } from 'lucide-react';
import { toast } from '@/features/shared/components/toast';
import { cn } from '@/lib/utils';

const DefaultAgentSelectorComponent = () => {
  const {
    adminSettings,
    assignDefaultAgent,
    removeDefaultAgent,
    toggleDefaultAgentStatus,
    isLoading,
  } = useAdminSettings();
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [isActive, setIsActive] = useState<boolean>(true);

  const currentDefaultAgentId = useMemo(() => {
    return adminSettings?.default_agent_settings?.default_agent_id || null;
  }, [adminSettings?.default_agent_settings?.default_agent_id]);

  const currentIsActive = useMemo(() => {
    return adminSettings?.default_agent_settings?.is_active ?? true;
  }, [adminSettings?.default_agent_settings?.is_active]);

  const currentAgentIds = useMemo((): string[] => {
    return currentDefaultAgentId ? [currentDefaultAgentId] : [];
  }, [currentDefaultAgentId]);

  const selectedAgentIds = useMemo((): string[] => {
    return selectedAgentId ? [selectedAgentId] : [];
  }, [selectedAgentId]);

  const { users: currentAgentDetails } = useUserDetails(currentAgentIds);
  const { users: selectedAgentDetails } = useUserDetails(selectedAgentIds);

  const currentAgent = useMemo(() => {
    return currentAgentDetails[0] || null;
  }, [currentAgentDetails]);

  const selectedAgent = useMemo(() => {
    return selectedAgentDetails[0] || null;
  }, [selectedAgentDetails]);

  const displayAgent = useMemo(() => {
    return selectedAgent || currentAgent;
  }, [selectedAgent, currentAgent]);

  // Sync with current default agent from store
  useEffect(() => {
    if (selectedAgentId !== currentDefaultAgentId) {
      startTransition(() => {
        setSelectedAgentId(currentDefaultAgentId);
      });
    }
  }, [currentDefaultAgentId, selectedAgentId]);

  // Sync with current active state from store
  useEffect(() => {
    if (isActive !== currentIsActive) {
      startTransition(() => {
        setIsActive(currentIsActive);
      });
    }
  }, [currentIsActive, isActive]);

  const handleAgentChange = useCallback(
    async (value: string | string[]) => {
      const newAgentId = Array.isArray(value)
        ? value[0] || null
        : value || null;

      if (!newAgentId) {
        // This shouldn't happen in normal flow, but handle gracefully
        return;
      }

      startTransition(() => {
        setSelectedAgentId(newAgentId);
        // Automatically enable when agent is assigned
        setIsActive(true);
      });

      try {
        await assignDefaultAgent(newAgentId);

        const agentDetails = selectedAgentDetails?.[0];
        const agentName = agentDetails?.name || 'Selected agent';
        toast.success('Default Agent Assigned', {
          description: `${agentName} is now the default agent for new tickets`,
        });
      } catch (error) {
        startTransition(() => {
          setSelectedAgentId(currentDefaultAgentId);
          setIsActive(currentIsActive);
        });

        toast.error('Assignment Failed', {
          description: 'Unable to assign default agent. Please try again.',
        });
      }
    },
    [
      currentDefaultAgentId,
      currentIsActive,
      assignDefaultAgent,
      selectedAgentDetails,
    ]
  );

  const handleRemoveSelected = useCallback(async () => {
    startTransition(() => {
      setSelectedAgentId(null);
      setIsActive(false);
    });

    try {
      await removeDefaultAgent();
      toast.success('Default Agent Removed', {
        description:
          'New tickets will remain unassigned until manually assigned',
      });
    } catch (error) {
      startTransition(() => {
        setSelectedAgentId(currentDefaultAgentId);
        setIsActive(currentIsActive);
      });
      toast.error('Removal Failed', {
        description: 'Unable to remove default agent. Please try again.',
      });
    }
  }, [currentDefaultAgentId, currentIsActive, removeDefaultAgent]);

  const handleToggleActive = useCallback(async () => {
    const newActiveState = !isActive;

    startTransition(() => {
      setIsActive(newActiveState);
    });

    try {
      await toggleDefaultAgentStatus(newActiveState);
      toast.success(
        newActiveState
          ? 'Default Agent Activated'
          : 'Default Agent Deactivated',
        {
          description: newActiveState
            ? 'New tickets will be automatically assigned to the default agent'
            : 'Agent will remain assigned but inactive for new tickets',
        }
      );
    } catch (error) {
      startTransition(() => {
        setIsActive(currentIsActive);
      });
      toast.error('Toggle Failed', {
        description: 'Unable to update agent status. Please try again.',
      });
    }
  }, [isActive, toggleDefaultAgentStatus, currentIsActive]);

  return (
    <div className='space-y-6'>
      {/* Current Default Agent Display - Matches Ticket Detail Page Styling */}
      <div className='p-4 bg-muted/50 rounded-lg border border-muted'>
        <div className='flex items-center gap-3'>
          {displayAgent ? (
            <>
              <ProfileAvatar
                avatarUrl={displayAgent.avatar_url || null}
                name={displayAgent.name}
                email={displayAgent.email}
                clerkId={displayAgent.clerk_id || ''}
                className='h-10 w-10'
                fallbackClassName='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium'
              />
              <div className='flex-1'>
                <h4 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                  {displayAgent.name}
                </h4>
                <p className='text-xs text-gray-500 dark:text-gray-400'>
                  {displayAgent.email}
                </p>
              </div>
              <div className='flex items-center gap-2'>
                <Switch
                  checked={isActive}
                  onCheckedChange={handleToggleActive}
                  disabled={isLoading}
                />
                <span
                  className={cn(
                    'text-xs font-medium',
                    isActive
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-500 dark:text-gray-400'
                  )}
                >
                  {isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </>
          ) : (
            <>
              <div className='h-10 w-10 rounded-full bg-muted flex items-center justify-center'>
                <User className='w-5 h-5 text-muted-foreground' />
              </div>
              <div className='flex-1'>
                <h4 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                  No default agent set
                </h4>
                <p className='text-xs text-gray-500 dark:text-gray-400'>
                  Auto-assignment disabled
                </p>
              </div>
              <div className='flex items-center gap-2'>
                <div className='w-2 h-2 rounded-full bg-yellow-500' />
                <span className='text-xs text-yellow-600 dark:text-yellow-400 font-medium'>
                  Not Set
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Agent Selection */}
      <div className='space-y-3'>
        <Label htmlFor='default-agent' className='text-sm font-medium'>
          Select Default Agent
        </Label>
        <div className='space-y-2'>
          {selectedAgent ? (
            <div className='file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring items-center justify-between'>
              <span className='text-gray-900 dark:text-gray-100'>
                {selectedAgent.email}
              </span>
              <X
                className='h-4 w-4 cursor-pointer hover:bg-gray-300 rounded-full text-gray-500 hover:text-gray-700'
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveSelected();
                }}
              />
            </div>
          ) : (
            <UserAutocomplete
              value=''
              onChange={handleAgentChange}
              placeholder='Search for an agent or admin...'
              roleFilter={['admin', 'agent']}
              multiple={false}
              dropdownOnly={true}
              returnUserIds={true}
              className='h-10'
              disabled={isLoading}
            />
          )}
          <p className='text-xs text-muted-foreground'>
            This agent will be automatically assigned to new tickets when no
            department-specific rules apply.
          </p>
        </div>
      </div>

      {/* Information Box */}
      <div className='bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4'>
        <div className='flex items-start gap-3'>
          <AlertCircle className='w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0' />
          <div className='space-y-2'>
            <h4 className='text-sm font-medium text-blue-900 dark:text-blue-100'>
              How Default Agent Assignment Works
            </h4>
            <ul className='text-xs text-blue-800 dark:text-blue-200 space-y-1'>
              <li>
                • New tickets are first checked against department-specific
                assignment rules
              </li>
              <li>
                • If no department rule matches, the default agent is assigned
              </li>
              <li>• If no default agent is set, tickets remain unassigned</li>
              <li>
                • Only users with Admin or Agent roles can be selected as
                default agents
              </li>
              <li>
                • This setting applies to all new tickets created in your
                organization
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export const DefaultAgentSelector = memo(DefaultAgentSelectorComponent);
